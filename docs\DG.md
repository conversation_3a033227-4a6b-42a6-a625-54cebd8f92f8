# Recoater HMI - Developer Guide

This document provides technical details for developers working on the Recoater HMI. It includes an overview of the architecture and a detailed API reference.

## 1. Architecture

The system uses a decoupled backend/frontend architecture.

-   **`backend` (FastAPI):** Acts as a proxy and business logic layer. It communicates directly with the recoater hardware's API. It exposes a simplified REST API and a WebSocket to the frontend. This isolates the frontend from the complexities of the hardware API and allows for state management and background tasks (like polling).

-   **`frontend` (Vue.js):** A pure Single-Page Application (SPA) that handles all user interaction. It gets all its data from the backend via API calls and receives real-time updates through a WebSocket connection. It never communicates with the recoater directly.

## 2. Key Components

### Backend Components

-   **`backend/services/recoater_client.py`:** Core service class that implements methods for every call to the recoater's hardware API. Handles connection management, error handling, and provides a clean interface for hardware communication.

-   **`backend/app/api/recoater.py`:** FastAPI router defining the status-related REST endpoints (`/api/v1/status/` and `/api/v1/status/health`). Handles error responses and provides structured status information.

-   **`backend/app/main.py`:** Main FastAPI application with WebSocket support for real-time updates. Includes background polling task, CORS configuration, and application lifecycle management.

-   **`backend/.env`:** Environment configuration file containing recoater API settings, WebSocket configuration, and debug options.

### Frontend Components

-   **`frontend/src/components/StatusIndicator.vue`:** Real-time status indicator component that displays connection status with colored dots and manages WebSocket connections.

-   **`frontend/src/views/StatusView.vue`:** Main status page showing detailed system information, connection status, and error details.

-   **`frontend/src/stores/status.js`:** Pinia store for centralized status management, WebSocket handling, and state synchronization.

-   **`frontend/src/services/api.js`:** Axios-based API service for HTTP communication with the backend, including request/response interceptors.

-   **`frontend/src/App.vue`:** Main application shell with navigation menu and header layout.

### Development and Testing

-   **`backend/tests/`:** Comprehensive test suite using pytest and pytest-mock for API endpoints and service classes.

-   **`frontend/tests/`:** Vitest-based test suite for Vue components, stores, and services.

## 3. Current Implementation Status (Phase 1-4)

### Completed Features

**Backend (FastAPI):**
- ✅ Basic FastAPI application structure
- ✅ RecoaterClient service for hardware communication
- ✅ Status API endpoints (`/api/v1/status/`, `/api/v1/status/health`)
- ✅ **Axis API endpoints (`/api/v1/axis/*`) - Phase 2**
- ✅ **Recoater control API endpoints (`/api/v1/recoater/*`) - Phase 3**
- ✅ **Hopper/blade control API endpoints (`/api/v1/recoater/drums/*/blade/*`) - Phase 4**
- ✅ WebSocket support for real-time updates
- ✅ **WebSocket axis data integration - Phase 2**
- ✅ **WebSocket drum and blade data integration - Phase 3 & 4**
- ✅ **WebSocket leveler data integration - Phase 5**
- ✅ Background polling of recoater status
- ✅ Environment configuration management
- ✅ Comprehensive test coverage (120 tests passing)

**Frontend (Vue.js):**
- ✅ Vue 3 application with Vite build system
- ✅ Real-time status indicator component
- ✅ Status page with detailed system information
- ✅ **Complete Axis Control interface - Phase 2**
- ✅ **Complete Recoater Control interface with drum controls - Phase 3**
- ✅ **Complete Hopper Control interface with blade controls - Phase 4**
- ✅ Pinia store for state management
- ✅ **Enhanced store with axis, drum, blade, and leveler data support - Phase 2-5**
- ✅ WebSocket integration for live updates
- ✅ Responsive design and navigation
- ✅ Test coverage with Vitest (118 tests passing)

**Integration:**
- ✅ End-to-end connectivity between frontend and backend
- ✅ Real-time status updates via WebSocket
- ✅ Error handling and connection management
- ✅ Development environment setup

### Phase 2 Architecture (Axis Control) - COMPLETED

**Backend Components:**

1. **Extended RecoaterClient** (`backend/services/recoater_client.py`)
   - Axis control methods following hardware API patterns
   - Consistent error handling and type safety
   - Methods: `get_axis_status()`, `move_axis()`, `home_axis()`, `set_gripper_state()`

2. **Axis API Router** (`backend/app/api/axis.py`)
   - RESTful endpoints for all axis operations
   - Pydantic models for request/response validation
   - Comprehensive error handling and status codes
   - Endpoints for X/Z axis movement, homing, and gripper control

3. **WebSocket Integration** (`backend/app/main.py`)
   - Real-time axis status polling every 1 second
   - Axis data included in WebSocket broadcasts
   - Graceful handling of unavailable axis endpoints

**Frontend Components:**

1. **AxisView Component** (`frontend/src/views/AxisView.vue`)
   - Complete axis control interface with real-time updates
   - Composition API with reactive state management
   - Input validation and error handling
   - Responsive design with accessibility features

2. **Enhanced Status Store** (`frontend/src/stores/status.js`)
   - Axis data state management
   - WebSocket message handling for axis updates
   - Computed properties for connection status

3. **Extended API Service** (`frontend/src/services/api.js`)
   - Axios-based methods for all axis endpoints
   - Consistent error handling and response processing

### Phase 3 Architecture (Drum Control) - COMPLETED

**Backend Components:**

1. **Extended RecoaterClient** (`backend/services/recoater_client.py`)
   - Drum control methods for motion, ejection, and suction
   - Consistent error handling and type safety
   - Methods: `get_drum_status()`, `set_drum_motion()`, `set_drum_ejection()`, `set_drum_suction()`

2. **Recoater Controls API Router** (`backend/app/api/recoater_controls.py`)
   - RESTful endpoints for all drum operations
   - Pydantic models for request/response validation
   - Comprehensive error handling and status codes

3. **WebSocket Integration** (`backend/app/main.py`)
   - Real-time drum status polling every 1 second
   - Drum data included in WebSocket broadcasts

**Frontend Components:**

1. **RecoaterView Component** (`frontend/src/views/RecoaterView.vue`)
   - Complete drum control interface with real-time updates
   - DrumControl component integration
   - Responsive grid layout for multiple drums

2. **DrumControl Component** (`frontend/src/components/DrumControl.vue`)
   - Individual drum control with motion and pressure management
   - Real-time status updates and error handling

### Phase 4 Architecture (Hopper Control) - COMPLETED

**Backend Components:**

1. **Extended RecoaterClient** (`backend/services/recoater_client.py`)
   - Blade control methods for collective and individual screw motion
   - Methods: `get_blade_screws_info()`, `set_blade_screws_motion()`, `set_blade_screw_motion()`

2. **Blade Control API Endpoints** (`backend/app/api/recoater_controls.py`)
   - Collective blade motion endpoints (`/drums/{drum_id}/blade/screws/motion`)
   - Individual screw control endpoints (`/drums/{drum_id}/blade/screws/{screw_id}/motion`)
   - Comprehensive validation and error handling

3. **WebSocket Integration** (`backend/app/main.py`)
   - Real-time blade status polling and broadcasting
   - Blade data included in drum data structure

**Frontend Components:**

1. **HopperControl Component** (`frontend/src/components/HopperControl.vue`)
   - Collective blade motion control (absolute, relative, homing modes)
   - Individual screw control for fine-tuning
   - Real-time status indicators and position displays
   - Distance controls in micrometers (µm)

2. **RecoaterView Integration** (`frontend/src/views/RecoaterView.vue`)
   - Hopper controls section below drum controls
   - Event handling for motion started/cancelled events

### Phase 5 Implementation Details - Leveler Control ✅

**Backend Components:**

1. **Extended RecoaterClient** (`backend/services/recoater_client.py`)
   - `get_leveler_pressure()` - Get pressure information (maximum, target, value)
   - `set_leveler_pressure(target)` - Set target pressure in Pascals
   - `get_leveler_sensor()` - Get magnetic sensor state

2. **Leveler Control API Endpoints** (`backend/app/api/recoater_controls.py`)
   - `GET /api/v1/recoater/leveler/pressure` - Get pressure information
   - `PUT /api/v1/recoater/leveler/pressure` - Set pressure target
   - `GET /api/v1/recoater/leveler/sensor` - Get sensor state
   - Pydantic validation for pressure requests

3. **WebSocket Integration** (`backend/app/main.py`)
   - Real-time leveler status polling and broadcasting
   - Leveler data included in WebSocket messages as `leveler_data`

**Frontend Components:**

1. **LevelerControl Component** (`frontend/src/components/LevelerControl.vue`)
   - Pressure status display (current, target, maximum in Pa)
   - Pressure input control with validation
   - Magnetic sensor status indicator
   - Real-time updates and error handling

2. **RecoaterView Integration** (`frontend/src/views/RecoaterView.vue`)
   - Leveler control section below hopper controls
   - Event handling for pressure set events

### Phase 9 Implementation Details - Configuration Management ✅

**Backend Components:**

1. **Configuration API Router** (`backend/app/api/configuration.py`)
   - `GET /api/v1/config/` - Get current recoater configuration
   - `PUT /api/v1/config/` - Set recoater configuration
   - Pydantic models for validation:
     - `BuildSpaceDimensions` - Length and width validation
     - `ConfigurationRequest` - All configuration parameters
     - `ConfigurationResponse` - Structured response format
   - Configuration parameters matching hardware API:
     - `build_space_diameter` - Build space diameter [mm]
     - `build_space_dimensions` - Length and width [mm]
     - `ejection_matrix_size` - Number of ejection points
     - `gaps` - List of drum gaps [mm]
     - `resolution` - System resolution [µm]

2. **Updated Mock Client** (`backend/services/mock_recoater_client.py`)
   - Proper configuration schema matching hardware specification
   - Realistic default values for development mode
   - Maintains compatibility with existing RecoaterClient interface

3. **Main Application Integration** (`backend/app/main.py`)
   - Configuration router included with `/api/v1` prefix
   - Consistent with other API router patterns

**Frontend Components:**

1. **ConfigurationView Component** (`frontend/src/views/ConfigurationView.vue`)
   - Complete configuration interface replacing placeholder
   - **Build Space Configuration Section**:
     - Diameter input with validation
     - Length and width inputs with proper units
   - **System Configuration Section**:
     - Resolution input with µm units
     - Ejection matrix size input
   - **Dynamic Drum Gap Configuration**:
     - Add/remove gaps functionality
     - Individual gap inputs with validation
     - Minimum one gap requirement
   - **Action Buttons**:
     - Save configuration with loading states
     - Reset to defaults functionality
   - **State Management**:
     - Loading, error, and success states
     - Auto-clearing notifications
     - Comprehensive error handling

2. **API Service Extension** (`frontend/src/services/api.js`)
   - `getConfiguration()` - Fetch current configuration
   - `setConfiguration(config)` - Save configuration changes
   - Named export for better import flexibility

**Key Features:**
- Dynamic form management with add/remove gaps
- Real-time validation and error handling
- Responsive card-based layout
- Comprehensive test coverage (9 backend + 14 frontend tests)
- Hardware API compliance with openapi.json specification

### Planned Features (Future Phases)

**Phase 3 - Drum Control:** ✅ COMPLETED
- Drum control interfaces (suction, ejection, rotation)
- Drum status monitoring
- Pressure control systems

**Phase 4 - Hopper Control:** ✅ COMPLETED
- Blade/screw positioning controls
- Collective and individual screw motion
- Real-time blade position monitoring

**Phase 5 - Leveler Control:** ✅ COMPLETED
- Leveler pressure management
- Sensor monitoring
- Pressure control interface
- Real-time leveler status updates

**Phase 6-7 - Print Management:**
- Geometry file upload (.CLI, .PNG)
- Print job configuration and execution
- Layer preview functionality

**Phase 9 - Configuration:** ✅ COMPLETED
- System configuration management
- Build space parameters
- Advanced settings

## 4. Development Mode Architecture

### Mock Recoater Client

The system includes a comprehensive mock client for development without hardware:

**MockRecoaterClient** (`backend/services/mock_recoater_client.py`)
- Implements identical interface to RecoaterClient
- Provides realistic simulated data with dynamic updates
- Supports all axis operations, status polling, and job control
- Enables full frontend development without hardware dependency

### Development Mode Configuration

**Environment Setup:**
```bash
# In backend/.env
DEVELOPMENT_MODE=true  # Enables mock client
RECOATER_API_BASE_URL=http://172.16.17.224:8080  # Hardware URL (ignored in dev mode)
WEBSOCKET_POLL_INTERVAL=1.0
DEBUG=true
LOG_LEVEL=INFO
```

**Automatic Mode Detection:**
- Backend automatically detects `DEVELOPMENT_MODE` setting on startup
- Logs indicate which mode is active: "Running in development mode with mock recoater client"
- Same WebSocket and API behavior in both modes
- Easy switching between development and production modes

## 5. Development Workflow

### Running the Application

1. **Backend Development (with mock data):**
   ```bash
   cd backend
   pip install -r requirements.txt
   # Ensure DEVELOPMENT_MODE=true in .env
   python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

2. **Frontend Development:**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

3. **Running Tests:**
   ```bash
   # Backend tests (all 57 tests)
   cd backend && python -m pytest tests/ -v

   # Frontend tests (all 96 tests)
   cd frontend && npm test

   # Run specific test files
   cd backend && python -m pytest tests/test_axis_api.py -v
   cd frontend && npm test -- --run tests/AxisView.test.js
   ```

### Test Coverage Status

**Backend Tests (120/120 passing):**
- `test_status_api.py`: 8 tests for status endpoints
- `test_recoater_client.py`: 11 tests for hardware client
- `test_axis_api.py`: 14 tests for axis control endpoints
- `test_recoater_controls_api.py`: 24 tests for drum and blade control endpoints
- `test_leveler_api.py`: 11 tests for leveler control endpoints
- `test_blade_controls_api.py`: 11 tests for blade control endpoints
- `test_print_api.py`: 32 tests for print management endpoints
- `test_configuration_api.py`: 9 tests for configuration endpoints

**Frontend Tests (118/118 passing):**
- `StatusView.test.js`: 11 tests for status page
- `StatusIndicator.test.js`: 9 tests for status indicator
- `AxisView.test.js`: 15 tests for axis control interface
- `DrumControl.test.js`: 21 tests for drum control component
- `HopperControl.test.js`: 19 tests for hopper control component
- `LevelerControl.test.js`: 8 tests for leveler control component
- `RecoaterView.test.js`: 21 tests for recoater view integration
- `PrintView.test.js`: 10 tests for print view interface
- `ConfigurationView.test.js`: 14 tests for configuration interface
- `LevelerControl.test.js`: 8 tests for leveler control component
- `RecoaterView.test.js`: 18 tests for recoater view integration
- `api.test.js`: 3 tests for API service

### API Endpoints

**Status Endpoints:**
- `GET /api/v1/status/` - Get current system status
- `GET /api/v1/status/health` - Health check

**Axis Control Endpoints (Phase 2):**
- `GET /api/v1/axis/{axis}` - Get axis status (x, z)
- `POST /api/v1/axis/{axis}/motion` - Move axis with distance/speed
- `POST /api/v1/axis/{axis}/home` - Home axis to reference position
- `GET /api/v1/axis/{axis}/motion` - Get current motion status
- `DELETE /api/v1/axis/{axis}/motion` - Cancel ongoing motion
- `PUT /api/v1/axis/gripper/state` - Set gripper state (enabled/disabled)
- `GET /api/v1/axis/gripper/state` - Get current gripper state

**Drum Control Endpoints (Phase 3):**
- `GET /api/v1/recoater/drums/{drum_id}/motion` - Get drum motion status
- `POST /api/v1/recoater/drums/{drum_id}/motion` - Move drum with parameters
- `DELETE /api/v1/recoater/drums/{drum_id}/motion` - Cancel drum motion
- `PUT /api/v1/recoater/drums/{drum_id}/ejection` - Set ejection pressure
- `PUT /api/v1/recoater/drums/{drum_id}/suction` - Set suction pressure

**Hopper Control Endpoints (Phase 4):**
- `GET /api/v1/recoater/drums/{drum_id}/blade/screws` - Get blade screws info
- `GET /api/v1/recoater/drums/{drum_id}/blade/screws/motion` - Get blade motion status
- `POST /api/v1/recoater/drums/{drum_id}/blade/screws/motion` - Move blade (collective)
- `DELETE /api/v1/recoater/drums/{drum_id}/blade/screws/motion` - Cancel blade motion
- `POST /api/v1/recoater/drums/{drum_id}/blade/screws/{screw_id}/motion` - Move individual screw
- `DELETE /api/v1/recoater/drums/{drum_id}/blade/screws/{screw_id}/motion` - Cancel screw motion

**Leveler Control Endpoints (Phase 5):**
- `GET /api/v1/recoater/leveler/pressure` - Get leveler pressure information
- `PUT /api/v1/recoater/leveler/pressure` - Set leveler pressure target
- `GET /api/v1/recoater/leveler/sensor` - Get leveler sensor state

**WebSocket Endpoints:**
- `WebSocket /ws` - Real-time status, axis, drum, blade, and leveler updates

## 5. Recoater Hardware API Reference

This reference is auto-generated from the provided `openapi.json` file. The backend service (`recoater_client.py`) should implement methods to call these endpoints.

### Recoater Endpoints

#### `GET /config`

- **Summary:** Get the recoater configuration variables.

- **Description:** Returns the configuration variables of the recoater.

#### `PUT /config`

- **Summary:** Set the recoater configuration variables.

- **Description:** Defines the configuration variables of the recoater.

- **Request Body (`application/json`):**

  - `build_space_diameter` (number, optional): The diameter of the build space [mm].

  - `build_space_dimensions` (object, optional):

    - `length` (number): The length of the build space [mm].

    - `width` (number): The width of the build space [mm].

  - `ejection_matrix_size` (integer, optional): The number of points in the ejection matrix.

  - `gaps` (array, optional): The list of gaps between the drums.

  - `resolution` (integer, optional): The resolution of the recoater, the size of one pixel [µm].

#### `GET /drums`

- **Summary:** Get drums info.

- **Description:** Returns information about the drums.

#### `GET /drums/{drum_id}`

- **Summary:** Get drum info.

- **Description:** Returns information about the specified drum.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

#### `PUT /drums/{drum_id}/config`

- **Summary:** Set drum config.

- **Description:** Defines the configuration of the specified drum.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

- **Request Body (`application/json`):**

  - `powder_offset` (integer, optional): The drum's powder offset [pixel].

  - `theta_offset` (number, required): The drum's theta offset [mm].

#### `PUT /drums/{drum_id}/ejection`

- **Summary:** Set drum ejection pressure.

- **Description:** Defines the target ejection pressure that the specified drum has to reach.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

- **Request Body (`application/json`):**

  - `target` (number, required): The target ejection pressure of the drum.

  - `unit` (string, optional): Units: pascal, bar.

#### `PUT /drums/{drum_id}/geometry`

- **Summary:** Set the drum geometry.

- **Description:** Defines the geometry of the specified drum. The geometry can either be a PNG file or a CLI file.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

- **Request Body (`application/octet-stream`):** Binary data.

#### `DELETE /drums/{drum_id}/geometry`

- **Summary:** Delete the drum geometry.

- **Description:** Removes the current geometry of the specified drum.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

#### `POST /drums/{drum_id}/motion`

- **Summary:** Post a motion command.

- **Description:** Creates a motion command if possible.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

- **Request Body (`application/json`):**

  - `mode` (string, required): Motion's mode: absolute, relative, turns, speed, homing.

  - `speed` (number, required): The speed of the motion [mm/s].

  - `distance` (number, optional): The absolute or relative distance of the motion [mm].

  - `turns` (number, optional): The number of turns of the motion.

#### `DELETE /drums/{drum_id}/motion`

- **Summary:** Delete the current motion command.

- **Description:** Cancels and removes the current motion command.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

#### `PUT /drums/{drum_id}/suction`

- **Summary:** Set drum suction pressure target.

- **Description:** Defines the target suction pressure that the specified drum has to reach.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

- **Request Body (`application/json`):**

  - `target` (number, required): The target suction pressure of the drum [Pa].

#### `POST /drums/{drum_id}/blade/screws/motion`

- **Summary:** Post a motion command.

- **Description:** Creates a motion command if possible.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

- **Request Body (`application/json`):**

  - `mode` (string, required): Motion's mode: absolute, relative, homing.

  - `distance` (number, optional): The absolute or relative distance of the motion [µm].

#### `POST /drums/{drum_id}/blade/screws/{screw_id}/motion`

- **Summary:** Post a motion command.

- **Description:** Creates a motion command if possible for a single screw.

- **Parameters:**

  - `drum_id` (integer, path, required): The drum's ID.

  - `screw_id` (integer, path, required): The drum blade screw's ID.

- **Request Body (`application/json`):**

  - `distance` (number, required): The relative distance of the motion [µm].

#### `GET /layer/preview`

- **Summary:** Get layer preview.

- **Description:** Returns a PNG image preview of the layer.

#### `PUT /layer/parameters`

- **Summary:** Set layer's parameters.

- **Description:** Defines the parameters of the current layer.

- **Request Body (`application/json`):**

  - `filling_id` (integer, required): The ID of the drum with the filling material powder.

  - `speed` (number, required): The patterning speed [mm/s].

  - `powder_saving` (boolean, optional, default: true): A flag indicating if the powder saving strategies are used.

  - `x_offset` (number, optional): The offset along the X axis [mm].

#### `PUT /leveler/pressure`

- **Summary:** Set leveler pressure target.

- **Description:** Defines the target pressure that the leveler has to reach.

- **Request Body (`application/json`):**

  - `target` (number, required): The target pressure of the leveler [Pa].

#### `POST /print/job`

- **Summary:** Post a print job.

- **Description:** Creates a printing job if the server is ready to start.

#### `DELETE /print/job`

- **Summary:** Delete the current print job.

- **Description:** Cancels and removes the current printing job.

#### `GET /state`

- **Summary:** Get the recoater's state.

- **Description:** Returns the current state of the recoater (ready, printing, error).

---

*This is a summarized list. For full details on all endpoints and their responses, refer to the `openapi.json` file.*
