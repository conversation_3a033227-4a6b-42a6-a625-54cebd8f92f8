/**
 * Tests for ConfigurationView Component
 * =====================================
 * 
 * This module contains tests for the ConfigurationView component.
 * All tests use mocked API service to avoid backend dependencies.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import ConfigurationView from '../ConfigurationView.vue'
import { apiService } from '../../services/api.js'

// Mock the API service
vi.mock('../../services/api.js', () => ({
  apiService: {
    getConfiguration: vi.fn(),
    setConfiguration: vi.fn()
  }
}))

describe('ConfigurationView', () => {
  let wrapper

  const mockConfig = {
    build_space_diameter: 250.0,
    build_space_dimensions: {
      length: 250.0,
      width: 96.0
    },
    ejection_matrix_size: 192,
    gaps: [130.0, 130.0],
    resolution: 500
  }

  beforeEach(() => {
    // Reset all mocks before each test
    vi.clearAllMocks()
    
    // Setup default successful API responses
    apiService.getConfiguration.mockResolvedValue(mockConfig)
    apiService.setConfiguration.mockResolvedValue({ success: true })
  })

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount()
    }
  })

  it('renders the configuration view title', () => {
    wrapper = mount(ConfigurationView)
    
    const title = wrapper.find('h2.view-title')
    expect(title.exists()).toBe(true)
    expect(title.text()).toBe('Configuration')
  })

  it('loads configuration on mount', async () => {
    wrapper = mount(ConfigurationView)
    
    // Wait for the component to load
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))
    
    expect(apiService.getConfiguration).toHaveBeenCalledOnce()
  })

  it('displays loading state initially', async () => {
    // Mock a delayed response
    apiService.getConfiguration.mockImplementation(() => new Promise(() => {}))

    wrapper = mount(ConfigurationView)

    // Wait for the component to mount and start loading
    await wrapper.vm.$nextTick()

    const loadingState = wrapper.find('.loading-state')
    expect(loadingState.exists()).toBe(true)
    expect(loadingState.text()).toContain('Loading configuration...')
  })

  it('displays error state when loading fails', async () => {
    apiService.getConfiguration.mockRejectedValue(new Error('Network error'))
    
    wrapper = mount(ConfigurationView)
    
    // Wait for the error to be handled
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))
    
    const errorState = wrapper.find('.error-state')
    expect(errorState.exists()).toBe(true)
    expect(errorState.text()).toContain('Failed to load configuration')
  })

  it('displays configuration form when loaded successfully', async () => {
    wrapper = mount(ConfigurationView)
    
    // Wait for the component to load
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))
    
    const configForm = wrapper.find('.config-form')
    expect(configForm.exists()).toBe(true)
    
    // Check that input fields are present
    expect(wrapper.find('[data-testid="diameter-input"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="length-input"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="width-input"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="resolution-input"]').exists()).toBe(true)
    expect(wrapper.find('[data-testid="matrix-size-input"]').exists()).toBe(true)
  })

  it('populates form fields with loaded configuration', async () => {
    wrapper = mount(ConfigurationView)
    
    // Wait for the component to load
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))
    
    const diameterInput = wrapper.find('[data-testid="diameter-input"]')
    const lengthInput = wrapper.find('[data-testid="length-input"]')
    const widthInput = wrapper.find('[data-testid="width-input"]')
    const resolutionInput = wrapper.find('[data-testid="resolution-input"]')
    const matrixSizeInput = wrapper.find('[data-testid="matrix-size-input"]')
    
    expect(diameterInput.element.value).toBe('250')
    expect(lengthInput.element.value).toBe('250')
    expect(widthInput.element.value).toBe('96')
    expect(resolutionInput.element.value).toBe('500')
    expect(matrixSizeInput.element.value).toBe('192')
  })

  it('displays gap inputs correctly', async () => {
    wrapper = mount(ConfigurationView)
    
    // Wait for the component to load
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))
    
    const gap0Input = wrapper.find('[data-testid="gap-0-input"]')
    const gap1Input = wrapper.find('[data-testid="gap-1-input"]')
    
    expect(gap0Input.exists()).toBe(true)
    expect(gap1Input.exists()).toBe(true)
    expect(gap0Input.element.value).toBe('130')
    expect(gap1Input.element.value).toBe('130')
  })

  it('can add a new gap', async () => {
    wrapper = mount(ConfigurationView)
    
    // Wait for the component to load
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))
    
    const addGapButton = wrapper.find('[data-testid="add-gap-button"]')
    await addGapButton.trigger('click')
    
    const gap2Input = wrapper.find('[data-testid="gap-2-input"]')
    expect(gap2Input.exists()).toBe(true)
    expect(gap2Input.element.value).toBe('130')
  })

  it('can remove a gap', async () => {
    wrapper = mount(ConfigurationView)

    // Wait for the component to load
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))

    // Get the initial number of gaps
    const initialGapCount = wrapper.vm.config.gaps.length

    const removeGapButton = wrapper.find('[data-testid="remove-gap-button"]')
    await removeGapButton.trigger('click')

    // Wait for the DOM to update
    await wrapper.vm.$nextTick()

    // Verify we now have one less gap
    expect(wrapper.vm.config.gaps.length).toBe(initialGapCount - 1)

    // Check that the last gap input is no longer present
    const lastGapInput = wrapper.find(`[data-testid="gap-${initialGapCount - 1}-input"]`)
    expect(lastGapInput.exists()).toBe(false)
  })

  it('disables remove gap button when only one gap remains', async () => {
    // Mock config with only one gap
    const singleGapConfig = { ...mockConfig, gaps: [130.0] }
    apiService.getConfiguration.mockResolvedValue(singleGapConfig)
    
    wrapper = mount(ConfigurationView)
    
    // Wait for the component to load
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))
    
    const removeGapButton = wrapper.find('[data-testid="remove-gap-button"]')
    expect(removeGapButton.attributes('disabled')).toBeDefined()
  })

  it('saves configuration when save button is clicked', async () => {
    wrapper = mount(ConfigurationView)
    
    // Wait for the component to load
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))
    
    const saveButton = wrapper.find('[data-testid="save-button"]')
    await saveButton.trigger('click')
    
    expect(apiService.setConfiguration).toHaveBeenCalledWith(mockConfig)
  })

  it('displays success message after saving', async () => {
    wrapper = mount(ConfigurationView)
    
    // Wait for the component to load
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))
    
    const saveButton = wrapper.find('[data-testid="save-button"]')
    await saveButton.trigger('click')
    
    // Wait for save to complete
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))
    
    const successMessage = wrapper.find('.success-message')
    expect(successMessage.exists()).toBe(true)
    expect(successMessage.text()).toContain('Configuration saved successfully!')
  })

  it('resets configuration when reset button is clicked', async () => {
    wrapper = mount(ConfigurationView)
    
    // Wait for the component to load
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))
    
    // Modify a field
    const diameterInput = wrapper.find('[data-testid="diameter-input"]')
    await diameterInput.setValue('300')
    
    // Click reset
    const resetButton = wrapper.find('[data-testid="reset-button"]')
    await resetButton.trigger('click')
    
    // Check that the field is reset to original value
    expect(diameterInput.element.value).toBe('250')
  })

  it('handles save error gracefully', async () => {
    apiService.setConfiguration.mockRejectedValue(new Error('Save failed'))
    
    wrapper = mount(ConfigurationView)
    
    // Wait for the component to load
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))
    
    const saveButton = wrapper.find('[data-testid="save-button"]')
    await saveButton.trigger('click')
    
    // Wait for save to complete
    await wrapper.vm.$nextTick()
    await new Promise(resolve => setTimeout(resolve, 0))
    
    const errorState = wrapper.find('.error-state')
    expect(errorState.exists()).toBe(true)
    expect(errorState.text()).toContain('Failed to save configuration')
  })
})
