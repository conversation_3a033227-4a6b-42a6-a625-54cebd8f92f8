"""
Service for parsing binary CLI files and rendering individual layers as PNG images.

This service is designed for integration into the RecoaterHMI backend. It operates on
in-memory byte streams and returns structured Pydantic models or raw PNG bytes,
avoiding direct filesystem I/O.

--- HOW TO USE THIS SERVICE IN THE HMI (FOR THE IMPLEMENTING AGENT) ---

The intended workflow for Phase 10 is as follows:

1.  **File Upload (New Endpoint `POST /api/v1/print/cli/upload`):**
    - The user uploads a binary CLI file from the frontend.
    - The backend endpoint receives the `UploadFile`.
    - The endpoint reads the file's content into memory: `file_bytes = await upload_file.read()`.
    - An instance of `CliParserService` is created: `parser = CliParserService()`.
    - The bytes are parsed: `parsed_data = parser.parse(cli_bytes)`.
    - The returned `ParsedCliFile` object is stored in a simple, server-side cache
      (an in-memory dictionary is sufficient), keyed by a newly generated unique ID (e.g., UUID).
    - The endpoint returns the unique ID and the number of layers (`len(parsed_data.layers)`) to the frontend.

2.  **Layer Preview (New Endpoint `GET /api/v1/print/cli/{file_id}/layer/{layer_num}/preview`):**
    - The frontend, having received the file ID and total layer count, provides a UI
      for the user to select a layer number.
    - When the user requests a preview, the frontend calls this new GET endpoint.
    - The backend retrieves the cached `ParsedCliFile` object using the `file_id`.
    - It selects the correct layer from the `parsed_data.layers` list (adjusting for zero-based index).
    - It calls the render method: `png_bytes = parser.render_layer_to_png(layer)`.
    - The endpoint returns the `png_bytes` in a `fastapi.Response` with `media_type="image/png"`.

This decouples the heavyweight parsing operation from the lightweight rendering operation,
providing an efficient user experience.
"""
import struct
import io
import logging
from typing import List, Tuple, Optional
from pydantic import BaseModel
from PIL import Image, ImageDraw

# --- Custom Exception ---
class CliParsingError(Exception):
    """Custom exception for errors during CLI file parsing."""
    pass

# --- Pydantic Models for Structured Data ---
class Point(BaseModel):
    """Represents a single 2D point."""
    x: float
    y: float

class Polyline(BaseModel):
    """Represents a polyline with multiple points."""
    part_id: int
    direction: int
    points: List[Point]

class Hatch(BaseModel):
    """Represents a set of hatch lines."""
    group_id: int
    lines: List[Tuple[Point, Point]]

class CliLayer(BaseModel):
    """Represents all geometry data for a single layer at a specific Z-height."""
    z_height: float
    polylines: List[Polyline] = []
    hatches: List[Hatch] = []

class ParsedCliFile(BaseModel):
    """Represents the entire parsed CLI file, including header and layers."""
    header_lines: List[str]
    is_aligned: bool
    layers: List[CliLayer]

# --- Main Service Class ---
class CliParserService:
    """A service to handle parsing of binary CLI files and rendering of layers."""

    def __init__(self, logger: Optional[logging.Logger] = None):
        """Initializes the parser service."""
        self.logger = logger or logging.getLogger(__name__)

    def _read_and_unpack(self, stream: io.BytesIO, fmt: str, size: int) -> tuple:
        """Reads bytes from a stream and unpacks them."""
        data = stream.read(size)
        if len(data) < size:
            raise EOFError(f"Unexpected EOF. Wanted {size} bytes, got {len(data)}.")
        return struct.unpack(fmt, data)

    def parse(self, cli_byte_stream: bytes) -> ParsedCliFile:
        """
        Parses a binary CLI file from an in-memory byte stream.

        Args:
            cli_byte_stream: The raw bytes of the uploaded .cli file.

        Returns:
            A ParsedCliFile object containing the structured data.

        Raises:
            CliParsingError: If the header is malformed or data cannot be parsed.
        """
        stream = io.BytesIO(cli_byte_stream)

        # 1. Parse Header
        header_end_marker = b"$$HEADEREND"
        header_buffer = bytearray()
        while header_end_marker not in header_buffer:
            byte = stream.read(1)
            if not byte:
                raise CliParsingError(f"EOF reached before '{header_end_marker.decode()}' was found.")
            header_buffer.append(byte[0])
            if len(header_buffer) > 8192: # Safety limit
                raise CliParsingError("Header size exceeds 8KB limit.")

        header_str = header_buffer.decode('ascii', errors='replace')
        header_lines = [line.strip() for line in header_str.splitlines() if line.strip()]
        is_aligned = any("$$ALIGN" in line.upper() for line in header_lines)
        self.logger.info(f"CLI header parsed. Alignment detected: {is_aligned}")

        # 2. Parse Geometry
        layers: List[CliLayer] = []
        current_layer: Optional[CliLayer] = None

        while True:
            try:
                command_code = self._read_and_unpack(stream, "<H", 2)[0]
                if is_aligned:
                    stream.read(2)  # Skip 2 alignment bytes

                if command_code == 127:  # Layer
                    z = self._read_and_unpack(stream, "<f", 4)[0]
                    current_layer = CliLayer(z_height=z)
                    layers.append(current_layer)
                
                elif command_code == 130:  # Polyline
                    if not current_layer:
                        raise CliParsingError("Found Polyline data before a Layer was defined.")
                    part_id, direction, num_points = self._read_and_unpack(stream, "<iii", 12)
                    points = [Point(x=self._read_and_unpack(stream, "<f", 4)[0], y=self._read_and_unpack(stream, "<f", 4)[0]) for _ in range(num_points)]
                    current_layer.polylines.append(Polyline(part_id=part_id, direction=direction, points=points))

                elif command_code == 132:  # Hatches
                    if not current_layer:
                         raise CliParsingError("Found Hatch data before a Layer was defined.")
                    group_id, num_lines = self._read_and_unpack(stream, "<ii", 8)
                    hatches = []
                    for _ in range(num_lines):
                        x1, y1, x2, y2 = self._read_and_unpack(stream, "<ffff", 16)
                        hatches.append((Point(x=x1, y=y1), Point(x=x2, y=y2)))
                    current_layer.hatches.append(Hatch(group_id=group_id, lines=hatches))
                
                else:
                    self.logger.warning(f"Unsupported command code {command_code} at offset {stream.tell()}. Stopping parse.")
                    break

            except EOFError:
                self.logger.info("Successfully reached end of CLI geometry data.")
                break
            except struct.error as e:
                raise CliParsingError(f"Struct unpacking error at offset {stream.tell()}: {e}")

        return ParsedCliFile(header_lines=header_lines, is_aligned=is_aligned, layers=layers)

    def render_layer_to_png(self, layer: CliLayer, width: int = 800, height: int = 600) -> bytes:
        """
        Renders a single CliLayer object to a PNG byte string with autoscaling.

        Args:
            layer: The CliLayer object to render.
            width: The width of the output PNG image.
            height: The height of the output PNG image.

        Returns:
            A byte string containing the PNG image data.
        """
        image = Image.new("RGB", (width, height), "white")
        draw = ImageDraw.Draw(image)

        all_points = [p for poly in layer.polylines for p in poly.points]
        for hatch in layer.hatches:
            all_points.extend([h[0] for h in hatch.lines])
            all_points.extend([h[1] for h in hatch.lines])

        if not all_points:
            draw.text((10, 10), "No geometry in this layer", fill="black")
        else:
            # Determine bounding box of the geometry
            min_x = min(p.x for p in all_points)
            max_x = max(p.x for p in all_points)
            min_y = min(p.y for p in all_points)
            max_y = max(p.y for p in all_points)

            geo_width = max_x - min_x
            geo_height = max_y - min_y
            
            # Handle zero-dimension case
            if geo_width == 0 and geo_height == 0:
                 draw.point([(width / 2, height / 2)], fill="black")
            else:
                if geo_width == 0: geo_width = 1
                if geo_height == 0: geo_height = 1

                # Calculate scale factor to fit geometry, with a margin
                margin = 0.1
                scale_x = (width * (1 - 2 * margin)) / geo_width
                scale_y = (height * (1 - 2 * margin)) / geo_height
                scale = min(scale_x, scale_y)

                # Calculate offsets to center the geometry
                offset_x = (width - geo_width * scale) / 2
                offset_y = (height - geo_height * scale) / 2

                def transform(p: Point) -> tuple:
                    # Y-axis is inverted in PIL
                    px = (p.x - min_x) * scale + offset_x
                    py = (max_y - p.y) * scale + offset_y
                    return (px, py)

                # Draw polylines
                for poly in layer.polylines:
                    if len(poly.points) > 1:
                        draw.line([transform(p) for p in poly.points], fill="black", width=1)
                
                # Draw hatches
                for hatch in layer.hatches:
                    for line in hatch.lines:
                        draw.line((transform(line[0]), transform(line[1])), fill="blue", width=1)

        # Save to in-memory buffer
        img_buffer = io.BytesIO()
        image.save(img_buffer, format='PNG')
        return img_buffer.getvalue()