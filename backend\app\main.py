"""
Recoater HMI Backend - Main Application
=======================================

This is the main FastAPI application that serves as the backend for the
Recoater Custom HMI. It provides REST API endpoints and WebSocket connections
for real-time communication with the frontend.

The application acts as a proxy between the frontend and the recoater hardware,
providing a simplified and stable interface while handling all the complexity
of hardware communication.
"""

import os
import asyncio
import logging
from typing import Dict, Any
from contextlib import asynccontextmanager

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from dotenv import load_dotenv

from services.recoater_client import RecoaterConnectionError, RecoaterAPIError
from app.dependencies import initialize_recoater_client, get_recoater_client_instance
from app.api.status import router as status_router
from app.api.axis import router as axis_router
from app.api.recoater_controls import router as recoater_router
from app.api.print import router as print_router
from app.api.configuration import router as config_router

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=getattr(logging, os.getenv("LOG_LEVEL", "INFO")),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# WebSocket connection manager
class ConnectionManager:
    """Manages WebSocket connections for real-time updates."""
    
    def __init__(self):
        self.active_connections: list[WebSocket] = []
    
    async def connect(self, websocket: WebSocket):
        """Accept a new WebSocket connection."""
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")
    
    def disconnect(self, websocket: WebSocket):
        """Remove a WebSocket connection."""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")
    
    async def broadcast(self, message: Dict[str, Any]):
        """Broadcast a message to all connected clients."""
        if not self.active_connections:
            return
        
        # Remove disconnected clients
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_json(message)
            except Exception as e:
                logger.warning(f"Failed to send message to WebSocket: {e}")
                disconnected.append(connection)
        
        # Clean up disconnected clients
        for connection in disconnected:
            self.disconnect(connection)

manager = ConnectionManager()

async def status_polling_task():
    """Background task that polls recoater status and broadcasts updates."""
    poll_interval = float(os.getenv("WEBSOCKET_POLL_INTERVAL", "1.0"))

    while True:
        try:
            recoater_client = get_recoater_client_instance()
            if recoater_client and manager.active_connections:
                # Get current status from recoater
                status_data = recoater_client.get_state()

                # Get axis status information (only available in mock mode)
                axis_data = {}
                try:
                    # These methods only exist in MockRecoaterClient for development
                    if hasattr(recoater_client, 'get_axis_status'):
                        axis_data["x"] = recoater_client.get_axis_status("x")
                        axis_data["z"] = recoater_client.get_axis_status("z")
                    if hasattr(recoater_client, 'get_gripper_state'):
                        axis_data["gripper"] = recoater_client.get_gripper_state()
                except (RecoaterConnectionError, RecoaterAPIError) as axis_error:
                    # Axis endpoints are not available on real hardware
                    logger.debug(f"Axis status not available: {axis_error}")
                    axis_data = None

                # Get drum status information
                drum_data = {}
                try:
                    # Get drums info first to know how many drums exist
                    drums_info = recoater_client.get_drums()
                    if isinstance(drums_info, list):
                        for drum_info in drums_info:
                            drum_id = drum_info.get("id")
                            if drum_id is not None:
                                try:
                                    # Get detailed drum status
                                    drum_status = recoater_client.get_drum(drum_id)

                                    # Get drum motion status
                                    try:
                                        motion_status = recoater_client.get_drum_motion(drum_id)
                                    except (RecoaterConnectionError, RecoaterAPIError):
                                        motion_status = None

                                    # Get drum ejection status
                                    try:
                                        ejection_status = recoater_client.get_drum_ejection(drum_id)
                                    except (RecoaterConnectionError, RecoaterAPIError):
                                        ejection_status = None

                                    # Get drum suction status
                                    try:
                                        suction_status = recoater_client.get_drum_suction(drum_id)
                                    except (RecoaterConnectionError, RecoaterAPIError):
                                        suction_status = None

                                    # Get blade screws status
                                    try:
                                        blade_screws_info = recoater_client.get_blade_screws_info(drum_id)
                                    except (RecoaterConnectionError, RecoaterAPIError):
                                        blade_screws_info = None

                                    # Get blade screws motion status
                                    try:
                                        blade_screws_motion = recoater_client.get_blade_screws_motion(drum_id)
                                    except (RecoaterConnectionError, RecoaterAPIError):
                                        blade_screws_motion = None

                                    drum_data[str(drum_id)] = {
                                        "info": drum_status,
                                        "motion": motion_status,
                                        "ejection": ejection_status,
                                        "suction": suction_status,
                                        "blade_screws": blade_screws_info,
                                        "blade_motion": blade_screws_motion
                                    }
                                except (RecoaterConnectionError, RecoaterAPIError) as drum_error:
                                    logger.debug(f"Drum {drum_id} data not available: {drum_error}")
                                    continue
                except (RecoaterConnectionError, RecoaterAPIError) as drums_error:
                    # If drums endpoints are not available, continue without drum data
                    logger.debug(f"Drums data not available: {drums_error}")
                    drum_data = None

                # Get leveler status information
                leveler_data = {}
                try:
                    # Get leveler pressure status
                    try:
                        leveler_pressure = recoater_client.get_leveler_pressure()
                        leveler_data["pressure"] = leveler_pressure
                    except (RecoaterConnectionError, RecoaterAPIError):
                        leveler_data["pressure"] = None

                    # Get leveler sensor status
                    try:
                        leveler_sensor = recoater_client.get_leveler_sensor()
                        leveler_data["sensor"] = leveler_sensor
                    except (RecoaterConnectionError, RecoaterAPIError):
                        leveler_data["sensor"] = None

                except (RecoaterConnectionError, RecoaterAPIError) as leveler_error:
                    # If leveler endpoints are not available, continue without leveler data
                    logger.debug(f"Leveler data not available: {leveler_error}")
                    leveler_data = None

                # Get print status information
                print_data = {}
                try:
                    # Get layer parameters
                    try:
                        layer_parameters = recoater_client.get_layer_parameters()
                        print_data["layer_parameters"] = layer_parameters
                    except (RecoaterConnectionError, RecoaterAPIError):
                        print_data["layer_parameters"] = None

                    # Get print job status
                    try:
                        job_status = recoater_client.get_print_job_status()
                        print_data["job_status"] = job_status
                    except (RecoaterConnectionError, RecoaterAPIError):
                        print_data["job_status"] = None

                except (RecoaterConnectionError, RecoaterAPIError) as print_error:
                    # If print endpoints are not available, continue without print data
                    logger.debug(f"Print data not available: {print_error}")
                    print_data = None

                # Add timestamp and broadcast
                message = {
                    "type": "status_update",
                    "data": status_data,
                    "axis_data": axis_data,
                    "drum_data": drum_data,
                    "leveler_data": leveler_data,
                    "print_data": print_data,
                    "timestamp": asyncio.get_event_loop().time()
                }
                await manager.broadcast(message)
                
        except (RecoaterConnectionError, RecoaterAPIError) as e:
            # Broadcast connection error
            error_message = {
                "type": "connection_error",
                "error": str(e),
                "timestamp": asyncio.get_event_loop().time()
            }
            await manager.broadcast(error_message)
            logger.error(f"Status polling error: {e}")
        
        except Exception as e:
            logger.error(f"Unexpected error in status polling: {e}")
        
        await asyncio.sleep(poll_interval)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""

    # Startup
    logger.info("Starting Recoater HMI Backend...")

    # Initialize recoater client using the dependencies module
    initialize_recoater_client()

    # Start background status polling task
    polling_task = asyncio.create_task(status_polling_task())

    logger.info("Backend startup complete")

    yield

    # Shutdown
    logger.info("Shutting down Recoater HMI Backend...")
    polling_task.cancel()
    try:
        await polling_task
    except asyncio.CancelledError:
        pass
    logger.info("Backend shutdown complete")

# Create FastAPI application
app = FastAPI(
    title="Recoater HMI Backend",
    description="Backend API for the Aerosint SPD Recoater Custom HMI",
    version="1.0.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173"],  # Vite dev server
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include API routers
app.include_router(status_router, prefix="/api/v1")
app.include_router(axis_router, prefix="/api/v1")
app.include_router(recoater_router, prefix="/api/v1")
app.include_router(print_router, prefix="/api/v1")
app.include_router(config_router, prefix="/api/v1")

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time status updates."""
    await manager.connect(websocket)
    try:
        while True:
            # Keep connection alive and handle any incoming messages
            await websocket.receive_text()
    except WebSocketDisconnect:
        manager.disconnect(websocket)

@app.get("/")
async def root():
    """Root endpoint for basic health check."""
    return {
        "message": "Recoater HMI Backend",
        "version": "1.0.0",
        "status": "running"
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
